import React, { useState, useEffect, useCallback } from "react";
import { <PERSON>, <PERSON>, Card, Tabs, Spin } from "antd";
import {
  Line<PERSON><PERSON>Outlined,
  Pie<PERSON><PERSON>Outlined,
  Dot<PERSON><PERSON>Outlined,
} from "@ant-design/icons";
import { motion } from "framer-motion";
import toast from "react-hot-toast";

// Components
import FilterPanel from "@/components/filters/FilterPanel";
import Trend<PERSON>hart from "@/components/charts/TrendChart";
import StatusDistribution<PERSON><PERSON> from "@/components/charts/StatusDistributionChart";
import ProjectBreakdownChart from "@/components/charts/ProjectBreakdownChart";

import ErrorCategories<PERSON>hart from "@/components/charts/ErrorCategoriesChart";
import PerformanceHeatmapChart from "@/components/charts/PerformanceHeatmapChart";
import ComplexityBubbleChart from "@/components/charts/ComplexityBubbleChart";
import ErrorWordCloudChart from "@/components/charts/ErrorWordCloudChart";
import TestHierarchyChart from "@/components/charts/TestHierarchyChart";

// Services & Types
import apiService from "@/services/api";
import {
  DashboardMetrics,
  FilterCriteria,
  ChartData,
  TestAnalytics,
} from "@/types";

const { TabPane } = Tabs;

const TrendsInsights: React.FC = () => {
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [chartData, setChartData] = useState<Record<string, ChartData>>({});
  const [testAnalytics, setTestAnalytics] = useState<TestAnalytics[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<FilterCriteria>({});
  const [activeTab, setActiveTab] = useState("trends");

  const loadData = useCallback(
    async (showSuccessToast: boolean = false) => {
      try {
        setLoading(true);
        console.log("TrendsInsights: Loading data with filters:", filters);

        // Load dashboard metrics and test analytics in parallel
        const [metricsData, testAnalyticsData] = await Promise.all([
          apiService.getDashboardMetrics(filters),
          apiService.getTestAnalytics(filters),
        ]);

        setMetrics(metricsData);
        setTestAnalytics(testAnalyticsData);

        // Load various chart data (removed test-duration since we use metrics.trendData)
        const chartTypes = [
          "pass-fail-trend",
          "status-distribution",
          "project-breakdown",
        ];
        const chartPromises = chartTypes.map((type) =>
          apiService
            .getChartData(type, filters)
            .then((data) => ({ type, data }))
        );

        const chartResults = await Promise.all(chartPromises);
        const chartDataMap: Record<string, ChartData> = {};
        chartResults.forEach(({ type, data }) => {
          chartDataMap[type] = data;
        });
        setChartData(chartDataMap);

        if (showSuccessToast) {
          toast.success("Trends and insights loaded successfully");
        }
      } catch (error) {
        toast.error("Failed to load trends and insights");
      } finally {
        setLoading(false);
      }
    },
    [filters]
  );

  useEffect(() => {
    loadData();
  }, [loadData]);

  const handleFilterChange = (newFilters: FilterCriteria) => {
    console.log("TrendsInsights: Filter change received:", newFilters);
    setFilters(newFilters);
  };

  const handleRefresh = () => {
    loadData(true); // Show toast when explicitly refreshing
  };

  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  if (loading && !metrics) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {/* Filters */}
      <FilterPanel
        key={JSON.stringify(filters)}
        onFilterChange={handleFilterChange}
        onRefresh={handleRefresh}
        refreshLoading={loading}
        initialFilters={filters}
      />

      {/* Tabs for different views */}
      <Card>
        <Tabs activeKey={activeTab} onChange={handleTabChange} size="large">
          <TabPane
            tab={
              <span>
                <LineChartOutlined />
                Trend Analysis
              </span>
            }
            key="trends"
          >
            <Row gutter={[24, 24]}>
              <Col xs={24}>
                <Card title="Pass/Fail Rate Trends" className="h-96">
                  <TrendChart
                    data={metrics?.trendData || []}
                    loading={loading}
                  />
                </Card>
              </Col>

              <Col xs={24} lg={12}>
                <Card title="Test Duration Trends" className="h-80">
                  <TrendChart
                    data={metrics?.trendData || []}
                    loading={loading}
                    type="duration"
                  />
                </Card>
              </Col>

              <Col xs={24} lg={12}>
                <Card title="Retry Rate Trends" className="h-80">
                  <TrendChart
                    data={
                      metrics?.trendData?.map((point) => ({
                        ...point,
                        retryRate: metrics?.mostRetriedTests?.length
                          ? metrics.mostRetriedTests.reduce(
                              (sum, test) => sum + test.retryCount,
                              0
                            ) / metrics.mostRetriedTests.length
                          : 0,
                      })) || []
                    }
                    loading={loading}
                    type="retry"
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane
            tab={
              <span>
                <PieChartOutlined />
                Distribution Analysis
              </span>
            }
            key="distribution"
          >
            <Row gutter={[24, 24]}>
              <Col xs={24} lg={12}>
                <Card title="Test Status Distribution" className="h-96">
                  <StatusDistributionChart
                    data={{
                      passed: metrics?.passedTests || 0,
                      failed: metrics?.failedTests || 0,
                      skipped: metrics?.skippedTests || 0,
                      flaky: metrics?.testsByStatus?.flaky || 0,
                    }}
                    loading={loading}
                  />
                </Card>
              </Col>

              <Col xs={24} lg={12}>
                <Card title="Tests by Project" className="h-96">
                  <ProjectBreakdownChart
                    data={metrics?.testsByProject || {}}
                    loading={loading}
                  />
                </Card>
              </Col>

              <Col xs={24} lg={12}>
                <Card title="Error Categories" className="h-80">
                  <ErrorCategoriesChart
                    data={metrics?.errorCategories || {}}
                    loading={loading}
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane
            tab={
              <span>
                <DotChartOutlined />
                Advanced Visualizations
              </span>
            }
            key="advanced"
          >
            <Row gutter={[24, 24]}>
              <Col xs={24} lg={12}>
                <Card title="Test Performance Heatmap" className="h-96">
                  <PerformanceHeatmapChart
                    data={metrics?.filePathMetrics || {}}
                    loading={loading}
                  />
                </Card>
              </Col>

              <Col xs={24} lg={12}>
                <Card title="Test Complexity vs Success Rate" className="h-96">
                  <ComplexityBubbleChart
                    data={testAnalytics}
                    loading={loading}
                  />
                </Card>
              </Col>

              <Col xs={24}>
                <Card
                  className="h-[650px] shadow-sm"
                  styles={{
                    body: {
                      padding: 0,
                      height: "calc(650px - 57px)", // Subtract header height
                      overflow: "hidden",
                    },
                  }}
                >
                  <TestHierarchyChart
                    data={testAnalytics}
                    loading={loading}
                    height={593} // 650 - 57 (header)
                  />
                </Card>
              </Col>

              <Col xs={24}>
                <Card
                  className="h-96 shadow-sm"
                  styles={{
                    body: {
                      padding: 0,
                      height: "calc(384px - 57px)", // Subtract header height
                      overflow: "hidden",
                    },
                  }}
                >
                  <ErrorWordCloudChart
                    data={testAnalytics}
                    loading={loading}
                    height={327} // 384 - 57 (header)
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </Card>
      <br />
      {/* Key Insights Summary */}
      <Card title="Key Insights">
        <Row gutter={[24, 24]}>
          <Col xs={24} md={8}>
            <div className="text-center p-6 bg-gradient-to-r from-green-50 to-green-100 rounded-lg">
              <div className="text-2xl font-bold text-green-600 mb-2">
                {metrics?.overallPassRate.toFixed(1)}%
              </div>
              <div className="text-sm text-green-700">Overall Pass Rate</div>
              <div className="text-xs text-green-600 mt-1">
                {metrics?.passedTests} of {metrics?.totalTests} tests passing
              </div>
            </div>
          </Col>

          <Col xs={24} md={8}>
            <div className="text-center p-6 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 mb-2">
                {((metrics?.averageDuration || 0) / 1000).toFixed(1)}s
              </div>
              <div className="text-sm text-blue-700">Average Test Duration</div>
              <div className="text-xs text-blue-600 mt-1">
                Total: {((metrics?.totalDuration || 0) / 1000 / 60).toFixed(1)}{" "}
                minutes
              </div>
            </div>
          </Col>

          <Col xs={24} md={8}>
            <div className="text-center p-6 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg">
              <div className="text-2xl font-bold text-purple-600 mb-2">
                {Object.keys(metrics?.testsByProject || {}).length}
              </div>
              <div className="text-sm text-purple-700">Active Projects</div>
              <div className="text-xs text-purple-600 mt-1">
                Across {metrics?.totalTests} test cases
              </div>
            </div>
          </Col>
        </Row>
      </Card>
    </motion.div>
  );
};

export default TrendsInsights;
