import React from "react";
import { Link } from "react-router-dom";
import { Button, Avatar, Typography, Dropdown, message } from "antd";
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ReloadOutlined,
  UserOutlined,
  WifiOutlined,
  SettingOutlined,
  LogoutOutlined,
  ProfileOutlined,
  DownOutlined,
} from "@ant-design/icons";

const { Text } = Typography;

interface SimpleHeaderProps {
  collapsed: boolean;
  onToggleCollapse: () => void;
  pageTitle?: string;
}

const SimpleHeader: React.FC<SimpleHeaderProps> = ({
  collapsed,
  onToggleCollapse,
  pageTitle,
}) => {
  const handleRefresh = () => {
    window.location.reload();
  };

  const handleMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case "profile":
        // Navigate to profile page or open profile modal
        message.info("Profile page coming soon!");

        // navigate('/profile'); // Uncomment when profile page is available
        break;
      case "settings":
        // Navigate to settings page or open settings modal
        message.info("Settings page coming soon!");

        // navigate('/settings'); // Uncomment when settings page is available
        break;
      case "logout":
        // Handle logout logic
        message.loading("Logging out...", 1);
        setTimeout(() => {
          // Clear user session, tokens, etc.
          localStorage.removeItem("authToken");
          sessionStorage.clear();
          message.success("Successfully logged out!");
          // Redirect to login page or home
          setTimeout(() => {
            window.location.href = "/";
          }, 500);
        }, 1000);
        break;
      default:
        break;
    }
  };

  const userMenuItems = [
    {
      key: "profile",
      icon: <ProfileOutlined style={{ color: "#1890ff" }} />,
      label: (
        <div>
          <div style={{ fontWeight: 500 }}>Profile</div>
          <div style={{ fontSize: "12px", color: "#8c8c8c" }}>
            View and edit your profile
          </div>
        </div>
      ),
    },
    {
      key: "settings",
      icon: <SettingOutlined style={{ color: "#52c41a" }} />,
      label: (
        <div>
          <div style={{ fontWeight: 500 }}>Settings</div>
          <div style={{ fontSize: "12px", color: "#8c8c8c" }}>
            Configure your preferences
          </div>
        </div>
      ),
    },
    {
      type: "divider" as const,
    },
    {
      key: "logout",
      icon: <LogoutOutlined style={{ color: "#ff4d4f" }} />,
      label: (
        <div>
          <div style={{ fontWeight: 500, color: "#ff4d4f" }}>Logout</div>
          <div style={{ fontSize: "12px", color: "#8c8c8c" }}>
            Sign out of your account
          </div>
        </div>
      ),
      danger: true,
    },
  ];

  return (
    <header
      className="fixed top-0 left-0 right-0 w-full"
      style={{
        height: 64,
        background:
          "linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%)",
        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
        zIndex: 1001,
        borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        padding: "0 24px",
      }}
    >
      {/* Left Section - App Name and Menu Toggle */}
      <div
        className="flex items-center space-x-4"
        style={{
          flex: "0 0 auto",
          minWidth: "fit-content",
        }}
      >
        <Link
          to="/"
          className="flex items-center space-x-2 hover:bg-white hover:bg-opacity-10 p-1 rounded"
          style={{ textDecoration: "none" }}
        >
          <img
            src="logo.png"
            alt="Home"
            style={{
              width: "32px",
              height: "32px",
              objectFit: "contain",
              filter: "drop-shadow(0 0 4px white)",
            }}
          />
          &nbsp;&nbsp;&nbsp;
          <Text
            className="text-lg font-semibold whitespace-nowrap"
            style={{
              color: "white",
              margin: 0,
              fontSize: "20px",
              fontWeight: "600",
            }}
          >
            ZTB-E2E Analytics Dashboard
          </Text>
        </Link>
      </div>

      {/* Center Section - Page Title */}
      {pageTitle && (
        <div
          className="flex-1 flex justify-center items-center"
          style={{
            maxWidth: "400px",
            margin: "0 auto",
          }}
        >
          <Text
            className="text-xl font-semibold whitespace-nowrap"
            style={{
              color: "white",
              margin: 0,
              fontSize: "26px",
              fontWeight: "600",
              textShadow: "0 1px 2px rgba(0, 0, 0, 0.3)",
            }}
          >
            {pageTitle}
          </Text>
        </div>
      )}

      {/* Right Section - Connectivity Status, Refresh, and Avatar */}
      <div
        className="flex items-center space-x-6"
        style={{
          flex: "0 0 auto",
          marginLeft: "auto",
          gap: "24px",
        }}
      >
        <div
          className="flex items-center space-x-2 px-3 py-2 rounded-lg"
          style={{
            background: "rgba(16, 185, 129, 0.15)",
            border: "1px solid rgba(16, 185, 129, 0.3)",
            backdropFilter: "blur(8px)",
          }}
        >
          <WifiOutlined
            className="text-sm"
            style={{
              color: "#34d399",
              textShadow: "0 1px 2px rgba(0, 0, 0, 0.3)",
              filter: "drop-shadow(0 0 4px rgba(52, 211, 153, 0.4))",
            }}
          />
          &nbsp;&nbsp;
          <Text
            className="text-xs whitespace-nowrap font-medium"
            style={{
              color: "#ffffff",
              margin: 0,
              textShadow: "0 1px 2px rgba(0, 0, 0, 0.5)",
              fontWeight: "500",
            }}
          >
            Connected
          </Text>
        </div>

        <Button
          icon={<ReloadOutlined />}
          onClick={handleRefresh}
          size="small"
          style={{
            background: "rgba(255, 255, 255, 0.2)",
            border: "1px solid rgba(255, 255, 255, 0.3)",
            color: "white",
            borderRadius: "8px",
            fontWeight: "500",
            minWidth: "fit-content",
          }}
          className="hover:bg-white hover:bg-opacity-30 transition-all duration-200"
        >
          Refresh Application
        </Button>

        <Dropdown
          menu={{
            items: userMenuItems,
            onClick: handleMenuClick,
          }}
          placement="bottomRight"
          arrow={{ pointAtCenter: true }}
          trigger={["click"]}
          overlayStyle={{
            minWidth: "240px",
            borderRadius: "8px",
            boxShadow:
              "0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)",
          }}
        >
          <div
            className="flex items-center space-x-2 cursor-pointer px-3 py-2 rounded-lg transition-all duration-200"
            style={{
              background: "rgba(255, 255, 255, 0.1)",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              minWidth: "fit-content",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = "rgba(255, 255, 255, 0.2)";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = "rgba(255, 255, 255, 0.1)";
            }}
          >
            <Avatar
              size="small"
              icon={<UserOutlined />}
              style={{
                background: "linear-gradient(135deg, #10b981 0%, #059669 100%)",
                border: "none",
              }}
            />
            <div className="hidden md:block">
              <Text
                className="text-sm font-medium whitespace-nowrap"
                style={{ color: "white", margin: 0 }}
              >
                QA Team
              </Text>
            </div>
            <DownOutlined
              className="text-xs ml-1"
              style={{
                color: "white",
                opacity: 0.8,
                transition: "all 0.2s ease",
              }}
            />
          </div>
        </Dropdown>
      </div>
    </header>
  );
};

export default SimpleHeader;
