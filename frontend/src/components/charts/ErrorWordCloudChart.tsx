import React, { useMemo, useState } from "react";
import { Spin, Tooltip } from "antd";
import { TestAnalytics } from "@/types";

interface ErrorWordCloudChartProps {
  data: TestAnalytics[];
  loading?: boolean;
  height?: number;
}

interface WordData {
  word: string;
  count: number;
  fontSize: number;
  color: string;
  weight: string;
}

const ErrorWordCloudChart: React.FC<ErrorWordCloudChartProps> = ({
  data,
  loading = false,
  height = 300,
}) => {
  const [hoveredWord, setHoveredWord] = useState<string | null>(null);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Spin size="large" />
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        No error message data available
      </div>
    );
  }

  // Enhanced keyword extraction with better filtering
  const extractKeywords = (errorMessages: string[]) => {
    const keywords: Record<string, number> = {};

    // Common stop words to filter out
    const stopWords = new Set([
      "the",
      "a",
      "an",
      "and",
      "or",
      "but",
      "in",
      "on",
      "at",
      "to",
      "for",
      "of",
      "with",
      "by",
      "is",
      "are",
      "was",
      "were",
      "be",
      "been",
      "being",
      "have",
      "has",
      "had",
      "do",
      "does",
      "did",
      "will",
      "would",
      "could",
      "should",
      "may",
      "might",
      "can",
      "this",
      "that",
      "these",
      "those",
      "i",
      "you",
      "he",
      "she",
      "it",
      "we",
      "they",
      "me",
      "him",
      "her",
      "us",
      "them",
      "my",
      "your",
      "his",
      "her",
      "its",
      "our",
      "their",
      "test",
      "tests",
      "spec",
      "specs",
      "file",
      "files",
    ]);

    errorMessages.forEach((message) => {
      if (!message) return;

      // Extract meaningful words (3+ characters, not stop words)
      const words = message
        .toLowerCase()
        .replace(/[^\w\s]/g, " ")
        .split(/\s+/)
        .filter(
          (word) =>
            word.length >= 3 && !stopWords.has(word) && !/^\d+$/.test(word) // Filter out pure numbers
        );

      words.forEach((word) => {
        keywords[word] = (keywords[word] || 0) + 1;
      });
    });

    return keywords;
  };

  // Process error messages from test data
  const errorMessages = useMemo(() => {
    const messages: string[] = [];

    data.forEach((test) => {
      if (
        (test.lastStatus === "failed" ||
          test.lastStatus === "unexpected" ||
          test.lastStatus === "timeout") &&
        test.errorMessages &&
        test.errorMessages.length > 0
      ) {
        messages.push(...test.errorMessages);
      }
    });

    return messages;
  }, [data]);

  const wordData = useMemo(() => {
    const keywords = extractKeywords(errorMessages);
    const entries = Object.entries(keywords);

    if (entries.length === 0) return [];

    // Sort by frequency and take top 50 words
    const sortedEntries = entries.sort(([, a], [, b]) => b - a).slice(0, 50);

    const maxCount = Math.max(...sortedEntries.map(([, count]) => count));
    const minCount = Math.min(...sortedEntries.map(([, count]) => count));

    // Color palette for different frequency ranges
    const colors = [
      "#ef4444", // red-500
      "#f97316", // orange-500
      "#eab308", // yellow-500
      "#22c55e", // green-500
      "#3b82f6", // blue-500
      "#8b5cf6", // violet-500
      "#ec4899", // pink-500
    ];

    return sortedEntries.map(([word, count], index) => {
      const frequency = (count - minCount) / (maxCount - minCount || 1);
      const fontSize = Math.max(12 + frequency * 24, 12); // 12px to 36px
      const colorIndex = Math.floor(frequency * (colors.length - 1));

      return {
        word,
        count,
        fontSize,
        color: colors[colorIndex],
        weight:
          frequency > 0.7 ? "bold" : frequency > 0.4 ? "semibold" : "normal",
      } as WordData;
    });
  }, [errorMessages]);

  if (wordData.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-gray-500 bg-gray-50 rounded-lg">
        <div className="text-lg mb-2">📝</div>
        <div className="text-sm font-medium">No error keywords found</div>
        <div className="text-xs mt-1">
          {errorMessages.length === 0
            ? "No failed tests with error messages"
            : "Error messages contain no meaningful keywords"}
        </div>
      </div>
    );
  }

  return (
    <div style={{ height: height }} className="flex flex-col overflow-hidden">
      {/* Header */}
      <div className="flex-shrink-0 px-4 py-3 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-semibold text-gray-800">
              Error Message Word Cloud
            </h4>
            <div className="text-xs text-gray-600">
              Most frequent keywords from failed test error messages
            </div>
          </div>
          <div className="text-xs text-gray-500">
            {wordData.length} keywords • {errorMessages.length} errors
          </div>
        </div>
      </div>

      {/* Word Cloud Content */}
      <div className="flex-1 overflow-auto">
        <div className="p-6">
          <div className="flex flex-wrap gap-3 justify-center items-center min-h-[200px]">
            {wordData.map((word, index) => (
              <Tooltip
                key={word.word}
                title={`"${word.word}" appears ${word.count} time${
                  word.count > 1 ? "s" : ""
                } in error messages`}
                placement="top"
              >
                <span
                  className={`cursor-pointer transition-all duration-300 hover:scale-110 select-none ${
                    hoveredWord === word.word
                      ? "opacity-100 z-10"
                      : "opacity-85"
                  }`}
                  style={{
                    fontSize: `${word.fontSize}px`,
                    color: word.color,
                    fontWeight: word.weight,
                    textShadow:
                      hoveredWord === word.word
                        ? `0 0 12px ${word.color}40, 0 2px 4px rgba(0,0,0,0.2)`
                        : "0 1px 2px rgba(0,0,0,0.1)",
                    filter:
                      hoveredWord === word.word ? "brightness(1.1)" : "none",
                    transform:
                      hoveredWord === word.word ? "translateY(-2px)" : "none",
                  }}
                  onMouseEnter={() => setHoveredWord(word.word)}
                  onMouseLeave={() => setHoveredWord(null)}
                >
                  {word.word}
                </span>
              </Tooltip>
            ))}
          </div>
        </div>
      </div>

      {/* Footer Statistics */}
      <div className="flex-shrink-0 px-4 py-3 bg-gray-50 border-t border-gray-200">
        <div className="flex items-center justify-between text-xs">
          <div className="text-gray-600">
            <span className="font-medium text-blue-600">{wordData.length}</span>{" "}
            unique keywords from{" "}
            <span className="font-medium text-red-600">
              {errorMessages.length}
            </span>{" "}
            error messages
          </div>
          <div className="text-gray-500">
            Hover for frequency • Size indicates popularity
          </div>
        </div>
      </div>
    </div>
  );
};

export default ErrorWordCloudChart;
