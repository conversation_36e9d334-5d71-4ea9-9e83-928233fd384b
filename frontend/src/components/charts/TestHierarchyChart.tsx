import React, { useState, useMemo } from "react";
import { Spin, Input, <PERSON><PERSON>, Toolt<PERSON>, Badge } from "antd";
import {
  FolderOpenOutlined,
  FolderOutlined,
  FileTextOutlined,
  ExpandAltOutlined,
  CompressOutlined,
} from "@ant-design/icons";
import { TestAnalytics } from "@/types";

const { Search } = Input;

interface TestHierarchyChartProps {
  data: TestAnalytics[];
  loading?: boolean;
  height?: number;
}

interface HierarchyNode {
  name: string;
  tests: TestAnalytics[];
  children: Record<string, HierarchyNode>;
  passRate: number;
  totalTests: number;
  isExpanded?: boolean;
  path: string;
}

const TestHierarchyChart: React.FC<TestHierarchyChartProps> = ({
  data,
  loading = false,
}) => {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState("");
  const [expandAll, setExpandAll] = useState(false);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Spin size="large" />
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-gray-500 bg-gray-50 rounded-lg">
        <div className="text-lg mb-2">📁</div>
        <div className="text-sm font-medium">
          No test hierarchy data available
        </div>
        <div className="text-xs mt-1">
          Test file structure will appear here when data is loaded
        </div>
      </div>
    );
  }

  // Build hierarchy from file paths with enhanced structure
  const hierarchy = useMemo(() => {
    const buildHierarchy = (): Record<string, HierarchyNode> => {
      const hierarchy: Record<string, HierarchyNode> = {};

      data.forEach((test) => {
        if (!test.filePath) return;

        const parts = test.filePath.split("/").filter(Boolean);
        let currentLevel = hierarchy;
        let currentPath = "";

        parts.forEach((part, index) => {
          currentPath = currentPath ? `${currentPath}/${part}` : part;

          if (!currentLevel[part]) {
            currentLevel[part] = {
              name: part,
              tests: [],
              children: {},
              passRate: 0,
              totalTests: 0,
              path: currentPath,
            };
          }

          // If this is the last part (file), add the test
          if (index === parts.length - 1) {
            currentLevel[part].tests.push(test);
          }

          currentLevel = currentLevel[part].children;
        });
      });

      // Calculate pass rates for each node
      const calculatePassRates = (nodes: Record<string, HierarchyNode>) => {
        Object.values(nodes).forEach((node) => {
          // First calculate for children
          calculatePassRates(node.children);

          // Then calculate for this node
          let totalTests = node.tests.length;
          let passedTests = node.tests.filter(
            (test) =>
              test.lastStatus === "passed" || test.lastStatus === "expected"
          ).length;

          // Add children's stats
          Object.values(node.children).forEach((child) => {
            totalTests += child.totalTests;
            passedTests += Math.round(
              (child.passRate / 100) * child.totalTests
            );
          });

          node.totalTests = totalTests;
          node.passRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;
        });
      };

      calculatePassRates(hierarchy);
      return hierarchy;
    };

    return buildHierarchy();
  }, [data]);

  // Filter hierarchy based on search term
  const filteredHierarchy = useMemo(() => {
    if (!searchTerm.trim()) return hierarchy;

    const filterNodes = (
      nodes: Record<string, HierarchyNode>
    ): Record<string, HierarchyNode> => {
      const filtered: Record<string, HierarchyNode> = {};

      Object.entries(nodes).forEach(([key, node]) => {
        const matchesSearch =
          node.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          node.tests.some(
            (test) =>
              test.testName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
              test.filePath?.toLowerCase().includes(searchTerm.toLowerCase())
          );

        const filteredChildren = filterNodes(node.children);
        const hasMatchingChildren = Object.keys(filteredChildren).length > 0;

        if (matchesSearch || hasMatchingChildren) {
          filtered[key] = {
            ...node,
            children: filteredChildren,
          };
        }
      });

      return filtered;
    };

    return filterNodes(hierarchy);
  }, [hierarchy, searchTerm]);

  // Toggle node expansion
  const toggleNode = (path: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedNodes(newExpanded);
  };

  // Expand/collapse all nodes
  const handleExpandAll = () => {
    if (expandAll) {
      setExpandedNodes(new Set());
    } else {
      const allPaths = new Set<string>();
      const collectPaths = (nodes: Record<string, HierarchyNode>) => {
        Object.values(nodes).forEach((node) => {
          if (Object.keys(node.children).length > 0) {
            allPaths.add(node.path);
            collectPaths(node.children);
          }
        });
      };
      collectPaths(filteredHierarchy);
      setExpandedNodes(allPaths);
    }
    setExpandAll(!expandAll);
  };

  // Get pass rate color
  const getPassRateColor = (passRate: number) => {
    if (passRate >= 90) return "text-green-600 bg-green-50";
    if (passRate >= 70) return "text-yellow-600 bg-yellow-50";
    return "text-red-600 bg-red-50";
  };

  // Get pass rate badge color
  const getPassRateBadgeColor = (passRate: number) => {
    if (passRate >= 90) return "success";
    if (passRate >= 70) return "warning";
    return "error";
  };

  return (
    <div className="w-full h-full flex flex-col overflow-hidden">
      {/* Header with Controls */}
      <div className="flex-shrink-0 p-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
        <div className="flex items-center justify-between mb-3">
          <div>
            <h4 className="text-base font-semibold text-gray-800">
              Test File Hierarchy
            </h4>
            <div className="text-xs text-gray-600">
              Interactive file structure with pass rates • Click folders to
              expand/collapse
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              size="small"
              icon={expandAll ? <CompressOutlined /> : <ExpandAltOutlined />}
              onClick={handleExpandAll}
              type={expandAll ? "primary" : "default"}
              className="shadow-sm"
            >
              {expandAll ? "Collapse All" : "Expand All"}
            </Button>
          </div>
        </div>

        {/* Search Bar */}
        <div className="flex items-center gap-3">
          <Search
            placeholder="Search files, tests, or projects..."
            allowClear
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{ flex: 1, maxWidth: 350 }}
            size="small"
            className="shadow-sm"
          />

          <div className="text-xs text-gray-500 whitespace-nowrap bg-white px-2 py-1 rounded border">
            {Object.keys(filteredHierarchy).length} items
          </div>
        </div>
      </div>

      {/* Scrollable Content Area */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden p-4 bg-white">
        {Object.keys(filteredHierarchy).length === 0 ? (
          <div className="flex flex-col items-center justify-center h-32 text-gray-500 bg-gray-50 rounded-lg">
            <div className="text-lg mb-2">{searchTerm ? "🔍" : "📁"}</div>
            <div className="text-sm font-medium">
              {searchTerm
                ? "No matching files found"
                : "No hierarchy data available"}
            </div>
            <div className="text-xs mt-1">
              {searchTerm
                ? "Try adjusting your search terms"
                : "Test file structure will appear here"}
            </div>
          </div>
        ) : (
          <div className="space-y-1">
            {renderHierarchyLevel(filteredHierarchy)}
          </div>
        )}
      </div>

      {/* Footer with Legend */}
      <div className="flex-shrink-0 p-4 border-t border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 bg-green-100 border border-green-300 rounded shadow-sm"></div>
              <span className="text-green-700 font-medium">≥90% pass rate</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 bg-yellow-100 border border-yellow-300 rounded shadow-sm"></div>
              <span className="text-yellow-700 font-medium">
                70-89% pass rate
              </span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 bg-red-100 border border-red-300 rounded shadow-sm"></div>
              <span className="text-red-700 font-medium">
                &lt;70% pass rate
              </span>
            </div>
          </div>

          <div className="text-gray-600 flex items-center gap-4">
            <span className="bg-white px-2 py-1 rounded border shadow-sm">
              {data.length} total tests
            </span>
          </div>
        </div>
      </div>
    </div>
  );

  // Enhanced rendering with collapsible nodes
  function renderHierarchyLevel(
    nodes: Record<string, HierarchyNode>,
    level: number = 0
  ): React.ReactNode {
    const nodeEntries = Object.entries(nodes);
    if (nodeEntries.length === 0) return null;

    return (
      <div className="space-y-2">
        {nodeEntries.map(([, node]) => {
          const hasChildren = Object.keys(node.children).length > 0;
          const hasTests = node.tests.length > 0;
          const isExpanded = expandedNodes.has(node.path);
          const levelIndent = level * 24;

          if (!hasChildren && !hasTests) return null;

          return (
            <div key={node.path} className="relative mb-1">
              {/* Connection lines for visual hierarchy */}
              {level > 0 && (
                <div
                  className="absolute left-0 top-0 w-px h-6 bg-gray-300"
                  style={{ left: `${levelIndent - 12}px` }}
                />
              )}

              {/* Node content */}
              <div
                className={`flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-all duration-200 border border-transparent hover:border-gray-200 hover:shadow-sm ${getPassRateColor(
                  node.passRate
                )} ${
                  hasTests && !hasChildren ? "bg-blue-50 border-blue-200" : ""
                }`}
                style={{ marginLeft: `${levelIndent}px` }}
              >
                {/* Expand/collapse button for folders */}
                {hasChildren && (
                  <button
                    onClick={() => toggleNode(node.path)}
                    className="flex items-center justify-center w-6 h-6 rounded-md hover:bg-gray-200 transition-colors shadow-sm border border-gray-300"
                  >
                    {isExpanded ? (
                      <FolderOpenOutlined className="text-sm text-blue-600" />
                    ) : (
                      <FolderOutlined className="text-sm text-gray-600" />
                    )}
                  </button>
                )}

                {/* File icon for leaf nodes */}
                {!hasChildren && hasTests && (
                  <div className="w-6 h-6 flex items-center justify-center bg-gray-100 rounded-md border border-gray-300 shadow-sm">
                    <FileTextOutlined className="text-sm text-gray-600" />
                  </div>
                )}

                {/* Node name */}
                <div className="flex-1 min-w-0">
                  <div
                    className="font-semibold text-sm truncate text-gray-800"
                    title={node.name}
                  >
                    {node.name}
                  </div>
                  {hasTests && !hasChildren && (
                    <div className="text-xs text-blue-600 mt-0.5 font-medium">
                      {node.tests.length} test
                      {node.tests.length !== 1 ? "s" : ""} in this file
                    </div>
                  )}
                  {hasChildren && hasTests && (
                    <div className="text-xs text-gray-500 mt-0.5">
                      {node.tests.length} direct test
                      {node.tests.length !== 1 ? "s" : ""} + subfolders
                    </div>
                  )}
                  {hasChildren && !hasTests && (
                    <div className="text-xs text-gray-500 mt-0.5">
                      Folder with {Object.keys(node.children).length} item
                      {Object.keys(node.children).length !== 1 ? "s" : ""}
                    </div>
                  )}
                </div>

                {/* Pass rate badge */}
                <Tooltip
                  title={`${node.passRate.toFixed(1)}% pass rate (${
                    node.totalTests
                  } total tests)`}
                  placement="top"
                >
                  <div
                    className={`px-2 py-1 rounded-full text-xs font-bold shadow-sm border ${
                      node.passRate >= 90
                        ? "bg-green-100 text-green-800 border-green-300"
                        : node.passRate >= 70
                        ? "bg-yellow-100 text-yellow-800 border-yellow-300"
                        : "bg-red-100 text-red-800 border-red-300"
                    }`}
                  >
                    {node.passRate.toFixed(0)}%
                  </div>
                </Tooltip>

                {/* Test count */}
                <div className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded-full border border-gray-300 shadow-sm font-medium">
                  {node.totalTests} test{node.totalTests !== 1 ? "s" : ""}
                </div>
              </div>

              {/* Render children if expanded */}
              {hasChildren && isExpanded && (
                <div className="mt-2 mb-1">
                  {renderHierarchyLevel(node.children, level + 1)}
                </div>
              )}

              {/* Render individual tests for leaf nodes (files with tests but no subfolders) */}
              {hasTests && !hasChildren && (
                <div className="ml-8 mt-2 space-y-1 border-l-2 border-gray-200 pl-4">
                  {node.tests.map((test, index) => (
                    <div
                      key={`${test.filePath}-${test.testName}-${index}`}
                      className="flex items-center gap-3 p-2 text-xs rounded-md hover:bg-gray-50 border border-gray-200 bg-white shadow-sm transition-all duration-200"
                    >
                      {/* Status indicator */}
                      <div className="w-4 h-4 flex items-center justify-center flex-shrink-0">
                        <div
                          className={`w-3 h-3 rounded-full shadow-sm border ${
                            test.lastStatus === "passed" ||
                            test.lastStatus === "expected"
                              ? "bg-green-500 border-green-600"
                              : test.lastStatus === "failed" ||
                                test.lastStatus === "unexpected" ||
                                test.lastStatus === "timeout"
                              ? "bg-red-500 border-red-600"
                              : test.lastStatus === "flaky"
                              ? "bg-yellow-500 border-yellow-600"
                              : "bg-gray-400 border-gray-500"
                          }`}
                          title={`Status: ${test.lastStatus}`}
                        />
                      </div>

                      {/* Test name */}
                      <div
                        className="flex-1 truncate font-medium text-gray-700 min-w-0"
                        title={test.testName}
                      >
                        {test.testName}
                      </div>

                      {/* Duration */}
                      <div className="text-gray-600 bg-gray-100 px-2 py-1 rounded-full border border-gray-300 text-xs font-medium flex-shrink-0">
                        {((test.averageDuration || 0) / 1000).toFixed(1)}s
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  }
};

export default TestHierarchyChart;
