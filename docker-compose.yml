version: "3.8"

services:
  cbi-e2e-analytics:
    image: 091834058219.dkr.ecr.eu-central-1.amazonaws.com/cbi-e2e-analytics-v1.0.0:latest
    container_name: cbi-e2e-analytics
    ports:
      - "3000:3000" # Frontend
      - "8080:8080" # Backend API
    env_file:
      - backend/.env
    environment:
      # Only pass AWS credentials as environment variables (override .env if provided)
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN}
      # S3 Configuration - ensure these are passed to container
      - ENABLE_S3_INTEGRATION=true
      - AWS_REGION=eu-central-1
      - AWS_S3_BUCKET=appsulate-jenkins-reports
      - AWS_S3_DATA_PREFIX=workflowtest/dev/ourl-lemon/
      - AWS_S3_VIDEO_PREFIX=workflowtest/dev/ourl-lemon/
      - AWS_S3_SCREENSHOT_PREFIX=workflowtest/dev/ourl-lemon/
      - S3_SYNC_DAYS=3
      - S3_SYNC_INTERVAL_MINUTES=5
    volumes:
      # Mount the .env file to ensure it's available inside the container
      - ./backend/.env:/app/backend/.env:ro
      # Mount a local directory for persistent test result data
      - ./data:/app/backend/json-test-results
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - cbi-analytics-network

networks:
  cbi-analytics-network:
    driver: bridge
