package config

import (
	"log"
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

type Config struct {
	Server struct {
		Port string
		Host string
	}

	Database struct {
		URL string
	}

	Features struct {
		S3Integration bool
	}

	AWS struct {
		Region          string
		AccessKeyID     string
		SecretAccessKey string
		SessionToken    string
		S3Bucket        string
		S3VideoPrefix   string
		S3DataPrefix    string
		S3SyncInterval  int
		S3MaxFiles      int
		S3SyncDays      int
	}

	Slack struct {
		Token      string
		Channel    string
		WebhookURL string
	}
}

func Load() (*Config, error) {
	// Load .env file if it exists
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found or error loading .env file:", err)
	} else {
		log.Println("Successfully loaded .env file")
	}

	cfg := &Config{}

	// Server configuration
	cfg.Server.Port = getEnv("PORT", "8080")
	cfg.Server.Host = getEnv("HOST", "localhost")

	// Database configuration
	cfg.Database.URL = getEnv("DATABASE_URL", "./data/test_results.db")

	// Feature flags
	cfg.Features.S3Integration = getEnvBool("ENABLE_S3_INTEGRATION", false)

	// AWS configuration
	cfg.AWS.Region = getEnv("AWS_REGION", "eu-central-1")
	cfg.AWS.AccessKeyID = getEnv("AWS_ACCESS_KEY_ID", "")
	cfg.AWS.SecretAccessKey = getEnv("AWS_SECRET_ACCESS_KEY", "")
	cfg.AWS.SessionToken = getEnv("AWS_SESSION_TOKEN", "")
	cfg.AWS.S3Bucket = getEnv("AWS_S3_BUCKET", "")
	cfg.AWS.S3VideoPrefix = getEnv("AWS_S3_VIDEO_PREFIX", "workflowtest/dev/ourl-lemon/")
	cfg.AWS.S3DataPrefix = getEnv("AWS_S3_DATA_PREFIX", "workflowtest/dev/ourl-lemon/")
	cfg.AWS.S3SyncInterval = getEnvInt("S3_SYNC_INTERVAL_MINUTES", 5)
	cfg.AWS.S3MaxFiles = getEnvInt("S3_MAX_FILES", 10)
	cfg.AWS.S3SyncDays = getEnvInt("S3_SYNC_DAYS", 5)

	// Slack configuration
	cfg.Slack.Token = getEnv("SLACK_TOKEN", "")
	cfg.Slack.Channel = getEnv("SLACK_CHANNEL", "")
	cfg.Slack.WebhookURL = getEnv("SLACK_WEBHOOK_URL", "")

	// Debug output for S3 configuration
	log.Printf("S3 Integration: %v", cfg.Features.S3Integration)
	log.Printf("AWS S3 Bucket: %s", cfg.AWS.S3Bucket)
	log.Printf("AWS Region: %s", cfg.AWS.Region)
	log.Printf("AWS Data Prefix: %s", cfg.AWS.S3DataPrefix)

	return cfg, nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseBool(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.Atoi(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}
